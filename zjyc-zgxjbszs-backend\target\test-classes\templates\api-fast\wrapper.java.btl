/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package ${strutil.replace(package.Entity,"pojo.entity","wrapper")};

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import ${packageName!}.pojo.entity.${entityKey!}Entity;
import ${packageName!}.pojo.vo.${entityKey!}VO;
import java.util.Objects;

/**
 * ${table.comment!} 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since ${date!}
 */
public class ${entityKey!}Wrapper extends BaseEntityWrapper<${entityKey!}Entity, ${entityKey!}VO>  {

	public static ${entityKey!}Wrapper build() {
		return new ${entityKey!}Wrapper();
 	}

	@Override
	public ${entityKey!}VO entityVO(${entityKey!}Entity ${entityKeyPath!}) {
		${entityKey!}VO ${entityKeyPath!}VO = Objects.requireNonNull(BeanUtil.copyProperties(${entityKeyPath!}, ${entityKey!}VO.class));

		//User createUser = UserCache.getUser(${entityKeyPath!}.getCreateUser());
		//User updateUser = UserCache.getUser(${entityKeyPath!}.getUpdateUser());
		//${entityKeyPath!}VO.setCreateUserName(createUser.getName());
		//${entityKeyPath!}VO.setUpdateUserName(updateUser.getName());

		return ${entityKeyPath!}VO;
	}

}
