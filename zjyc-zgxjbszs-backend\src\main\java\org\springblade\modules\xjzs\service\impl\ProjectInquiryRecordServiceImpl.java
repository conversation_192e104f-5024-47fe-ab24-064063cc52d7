/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springblade.core.tool.api.R;
import org.springblade.modules.xjzs.pojo.entity.ProjectEntity;
import org.springblade.modules.xjzs.pojo.entity.ProjectInquiryRecordEntity;
import org.springblade.modules.xjzs.pojo.entity.SupplierInquiryRecordEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectInquiryRecordVO;
import org.springblade.modules.xjzs.excel.ProjectInquiryRecordExcel;
import org.springblade.modules.xjzs.mapper.ProjectInquiryRecordMapper;
import org.springblade.modules.xjzs.service.IProjectInquiryRecordService;
import org.springblade.modules.xjzs.service.IProjectService;
import org.springblade.modules.dify.util.FileProcessUtils;
import org.springblade.modules.xjzs.service.ISupplierInquiryRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.tool.utils.StringUtil;

/**
 * 项目询价记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
@Slf4j
public class ProjectInquiryRecordServiceImpl extends BaseServiceImpl<ProjectInquiryRecordMapper, ProjectInquiryRecordEntity> implements IProjectInquiryRecordService {

	@Autowired
	private IProjectService projectService;

	@Autowired
	private ISupplierInquiryRecordService supplierInquiryRecordService;

	@Override
	public IPage<ProjectInquiryRecordVO> selectProjectInquiryRecordPage(IPage<ProjectInquiryRecordVO> page, ProjectInquiryRecordVO projectInquiryRecord) {
		return page.setRecords(baseMapper.selectProjectInquiryRecordPage(page, projectInquiryRecord));
	}


	@Override
	public List<ProjectInquiryRecordExcel> exportProjectInquiryRecord(Wrapper<ProjectInquiryRecordEntity> queryWrapper) {
		List<ProjectInquiryRecordExcel> projectInquiryRecordList = baseMapper.exportProjectInquiryRecord(queryWrapper);
		//projectInquiryRecordList.forEach(projectInquiryRecord -> {
		//	projectInquiryRecord.setTypeName(DictCache.getValue(DictEnum.YES_NO, ProjectInquiryRecord.getType()));
		//});
		return projectInquiryRecordList;
	}

	@Override
	public Map<String, Object> getProjectInquiryList(String projectName, Integer current, Integer size) {
		// 创建查询条件
		LambdaQueryWrapper<ProjectInquiryRecordEntity> queryWrapper = new LambdaQueryWrapper<>();

		// 如果有项目名称参数，需要关联Project表进行查询
		// 由于ProjectInquiryRecordEntity只有projectId字段，没有projectName字段
		// 这里我们先根据projectId查询，后续可以优化为关联查询
		if (StringUtil.isNotBlank(projectName)) {
			// 获取符合条件的项目ID列表
			LambdaQueryWrapper<ProjectEntity> projectQueryWrapper = new LambdaQueryWrapper<>();
			projectQueryWrapper.like(ProjectEntity::getName, projectName);
			List<ProjectEntity> projectList = projectService.list(projectQueryWrapper);

			if (projectList != null && !projectList.isEmpty()) {
				List<Long> projectIds = projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList());
				queryWrapper.in(ProjectInquiryRecordEntity::getProjectId, projectIds);
			} else {
				// 如果没有找到匹配的项目，返回空结果
				Map<String, Object> emptyResult = new HashMap<>();
				emptyResult.put("records", Collections.emptyList());
				emptyResult.put("total", 0);
				emptyResult.put("size", size);
				emptyResult.put("current", current);
				return emptyResult;
			}
		}

		// 按创建时间降序排序
		queryWrapper.orderByDesc(ProjectInquiryRecordEntity::getCreateTime);

		// 执行分页查询
		Page<ProjectInquiryRecordEntity> page = new Page<>(current, size);
		Page<ProjectInquiryRecordEntity> resultPage = this.page(page, queryWrapper);

		// 将结果封装为Map返回
		Map<String, Object> result = new HashMap<>();
		result.put("records", resultPage.getRecords());
		result.put("total", resultPage.getTotal());
		result.put("size", resultPage.getSize());
		result.put("current", resultPage.getCurrent());

		return result;
	}

	@Override
	public boolean processQuotationDocument(MultipartFile file, Long inquiryId) {
		try {
			// 验证文件类型
			String fileName = file.getOriginalFilename();
			if (fileName == null || (!fileName.toLowerCase().endsWith(".docx") && !fileName.toLowerCase().endsWith(".doc"))) {
				log.error("文件格式不正确，只支持.doc和.docx格式");
				return false;
			}

			// 获取询价记录
			ProjectInquiryRecordEntity record = this.getById(inquiryId);
			if (record == null) {
				log.error("询价记录不存在，ID: {}", inquiryId);
				return false;
			}

			// 读取Word文档内容
			List<String> content = processWordDocument(file);
			if (content.isEmpty()) {
				log.error("无法读取文档内容");
				return false;
			}

			// 提取关键信息
			QuotationInfo quotationInfo = extractQuotationInfo(content);
			if (quotationInfo == null) {
				log.error("无法从文档中提取报价信息");
				return false;
			}

			// 构建查询条件：根据 projectId 和 supplier 查询唯一记录
			QueryWrapper<SupplierInquiryRecordEntity> wrapper = new QueryWrapper<>();
			wrapper.eq("project_id", record.getProjectId())
					.eq("supplier", quotationInfo.getCompany());

			SupplierInquiryRecordEntity supplierInquiryRecordEntity = supplierInquiryRecordService.getOne(wrapper, false); // 第二个参数为 false 表示不抛异常
			if(null == supplierInquiryRecordEntity){
				supplierInquiryRecordEntity = new SupplierInquiryRecordEntity();
			}

			supplierInquiryRecordEntity.setProjectInquiryId(inquiryId);
			supplierInquiryRecordEntity.setProjectId(record.getProjectId());
			supplierInquiryRecordEntity.setSupplier(quotationInfo.getCompany());
			supplierInquiryRecordEntity.setInquiryTime(quotationInfo.getQuotationDate());
			supplierInquiryRecordEntity.setInquiryChannel(""); // TODO
			supplierInquiryRecordEntity.setRemark(""); // TODO

			if("服务类".equals(record.getProjectType())) {
				supplierInquiryRecordEntity.setName(record.getServiceName());
				supplierInquiryRecordEntity.setContent(record.getServiceContent());
				supplierInquiryRecordEntity.setQuantity(1.0d);
				supplierInquiryRecordEntity.setUnitPrice(quotationInfo.getTotalAmount().doubleValue());
				supplierInquiryRecordEntity.setTotalPrice(quotationInfo.getTotalAmount().doubleValue());
			} else {
				supplierInquiryRecordEntity.setName(record.getProductName());
				supplierInquiryRecordEntity.setContent(record.getSpecifications());
				supplierInquiryRecordEntity.setQuantity(record.getQuantity());
				supplierInquiryRecordEntity.setUnitPrice(quotationInfo.getTotalAmount().doubleValue() / record.getQuantity());
				supplierInquiryRecordEntity.setTotalPrice(quotationInfo.getTotalAmount().doubleValue());
			}

			boolean updated = supplierInquiryRecordService.saveOrUpdate(supplierInquiryRecordEntity);

			// 更新询价记录
//			record.setQuotationTotalAmount(quotationInfo.getTotalAmount());
//			record.setQuotationCompany(quotationInfo.getCompany());
//			record.setQuotationTime(quotationInfo.getQuotationDate());
//			record.setStatus(2);
//			boolean updated = this.updateById(record);

			if (updated) {
				log.info("报价回函处理成功，询价ID: {}, 金额: {}, 公司: {}, 日期: {}",
					inquiryId, quotationInfo.getTotalAmount(), quotationInfo.getCompany(), quotationInfo.getQuotationDate());
			}

			return updated;

		} catch (Exception e) {
			log.error("处理报价回函失败，询价ID: {}", inquiryId, e);
			return false;
		}
	}

	@Override
	public boolean saveProjectInquiryRecord(ProjectInquiryRecordEntity projectInquiryRecord) {
		// 项目状态设为1：询价中
		LambdaUpdateWrapper<ProjectEntity> projectUpdateWrapper = new LambdaUpdateWrapper<>();
		projectUpdateWrapper.eq(ProjectEntity::getId, projectInquiryRecord.getProjectId())
				.set(ProjectEntity::getProjectStatus, 1);
		projectService.update(projectUpdateWrapper);
		return this.saveOrUpdate(projectInquiryRecord);
	}

	/**
	 * 处理Word文档，读取内容
	 */
	private List<String> processWordDocument(MultipartFile file) throws IOException {
		return FileProcessUtils.processWord(file);
	}

	/**
	 * 从文档内容中提取报价信息
	 */
	private QuotationInfo extractQuotationInfo(List<String> content) {
		QuotationInfo info = new QuotationInfo();

		for (String line : content) {
			log.debug("处理文档行: {}", line);

			// 提取总金额 - 匹配包含数字和"元"的模式
			if ((line.contains("总金额") || line.contains("报价")) && line.contains("元")) {
				BigDecimal amount = extractAmount(line);
				if (amount != null) {
					info.setTotalAmount(amount);
					log.info("提取到总金额: {}", amount);
				}
			}

			// 提取公司名称 - 匹配包含"有限公司"等关键词的文本，但排除"广东烟草湛江市有限公司"这种客户公司
			if ((line.contains("有限公司") || line.contains("股份有限公司") || line.contains("公司"))
				&& !line.contains("广东烟草") && !line.contains("湛江市有限公司")) {
				String company = extractCompany(line);
				if (company != null && !company.isEmpty()) {
					info.setCompany(company);
					log.info("提取到公司名称: {}", company);
				}
			}

			// 提取日期 - 匹配日期格式
			if (line.contains("日期") || line.matches(".*\\d{4}\\s*年\\s*\\d{1,2}\\s*月\\s*\\d{1,2}\\s*日.*")) {
				Date date = extractDate(line);
				if (date != null) {
					info.setQuotationDate(date);
					log.info("提取到日期: {}", date);
				}
			}
		}

		// 验证是否提取到了必要信息
		if (info.getTotalAmount() != null && info.getCompany() != null && info.getQuotationDate() != null) {
			log.info("成功提取报价信息 - 金额: {}, 公司: {}, 日期: {}",
				info.getTotalAmount(), info.getCompany(), info.getQuotationDate());
			return info;
		}

		log.warn("未能提取完整的报价信息 - 金额: {}, 公司: {}, 日期: {}",
			info.getTotalAmount(), info.getCompany(), info.getQuotationDate());
		return null;
	}

	/**
	 * 从文本中提取金额
	 */
	private BigDecimal extractAmount(String text) {
		// 匹配数字模式，支持小数点和逗号分隔符
		Pattern pattern = Pattern.compile("([0-9,]+(?:\\.[0-9]+)?)\\s*元");
		Matcher matcher = pattern.matcher(text);

		if (matcher.find()) {
			String amountStr = matcher.group(1).replace(",", "");
			try {
				return new BigDecimal(amountStr);
			} catch (NumberFormatException e) {
				log.warn("无法解析金额: " + amountStr);
			}
		}

		return null;
	}

	/**
	 * 从文本中提取公司名称
	 */
	private String extractCompany(String text) {
		// 匹配公司名称模式
		Pattern pattern = Pattern.compile("([\\u4e00-\\u9fa5a-zA-Z0-9]+(?:有限公司|股份有限公司|公司))");
		Matcher matcher = pattern.matcher(text);

		if (matcher.find()) {
			return matcher.group(1);
		}

		return null;
	}

	/**
	 * 从文本中提取日期
	 */
	public Date extractDate(String line) {
		// 支持空格的正则表达式
		Pattern pattern = Pattern.compile("(\\d{4})\\s*年\\s*(\\d{1,2})\\s*月\\s*(\\d{1,2})\\s*日");
		Matcher matcher = pattern.matcher(line);
		if (matcher.find()) {
			try {
				int year = Integer.parseInt(matcher.group(1));
				int month = Integer.parseInt(matcher.group(2));
				int day = Integer.parseInt(matcher.group(3));

				// 使用 SimpleDateFormat 解析（注意：非线程安全，建议局部使用）
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				return sdf.parse(year + "-" + month + "-" + day);
			} catch (Exception e) {
				log.warn("解析日期失败: {}", line, e);
			}
		}
		return null;
	}

	/**
	 * 报价信息内部类
	 */
	private static class QuotationInfo {
		private BigDecimal totalAmount;
		private String company;
		private Date quotationDate;

		public BigDecimal getTotalAmount() {
			return totalAmount;
		}

		public void setTotalAmount(BigDecimal totalAmount) {
			this.totalAmount = totalAmount;
		}

		public String getCompany() {
			return company;
		}

		public void setCompany(String company) {
			this.company = company;
		}

		public Date getQuotationDate() {
			return quotationDate;
		}

		public void setQuotationDate(Date quotationDate) {
			this.quotationDate = quotationDate;
		}
	}

}
