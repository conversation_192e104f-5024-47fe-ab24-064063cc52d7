package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.dify.service.DifyService;
import org.springblade.modules.dify.resp.BlockResponse;
import org.springblade.modules.dify.resp.StreamResponse;
import org.springblade.modules.dify.resp.WorkflowStreamResponse;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import org.springframework.http.MediaType;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审计控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/xjzs/audit")
@Tag(name = "审计接口", description = "审计接口")
public class AuditController {

    private final DifyService difyService;

    @Value("${dify.key.training:app-default}")
    private String calculationApiKey;

    @Value("${dify.key.goods:app-jimAmyz3ifsRVAt5FqExPubw}")
    private String goodsCalculationApiKey;

    @Value("${dify.key.engineering:app-engineering-default}")
    private String engineeringCalculationApiKey;

    @Value("${dify.key.inquiry:app-inquiry-default}")
    private String inquiryCalculationApiKey;

    
    /**
     * 使用Dify AI计算审计费用 - 原始query格式（兼容旧页面）
     */
    @PostMapping("/calculate")
    @Operation(summary = "使用Dify AI计算审计费用", description = "使用Dify AI引擎根据项目信息和标准表数据计算审计费用")
    @Parameters({
        @Parameter(name = "params", description = "审计参数")
    })
    public R<AuditResult> calculate(@RequestBody AuditParams params) {
        try {
            log.info("开始使用Dify AI计算审计费用: {}", params);

            // 构建Dify查询内容
            String query = buildDifyQuery(params);

            // 调用Dify API - 使用原始query格式
            BlockResponse response = difyService.blockingMessage(
                query,
                0L,
                calculationApiKey,
                null
            );

            if (response == null || response.getAnswer() == null) {
                log.error("Dify API返回空响应");
                return R.fail("AI计算服务暂时不可用");
            }

            // 解析Dify响应
            AuditResult result = parseDifyResponse(response.getAnswer(), params);

            if (result.isSuccess()) {
                return R.data(result);
            } else {
                return R.fail(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("使用Dify计算审计费用时发生错误", e);
            return R.fail("AI计算错误: " + e.getMessage());
        }
    }

    /**
     * 使用Dify AI计算审计费用 - 新的inputs格式（用于新页面）  暂未使用
     */
    @PostMapping("/calculate-with-inputs")
    @Operation(summary = "使用Dify AI计算审计费用 - inputs格式", description = "使用Dify AI引擎根据培训参数计算审计费用")
    @Parameters({
        @Parameter(name = "requestBody", description = "完整的Dify请求体")
    })
    public R<AuditResult> calculateWithInputs(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("开始使用Dify AI计算审计费用 (inputs格式): {}", requestBody);

            // 调用Dify API - 直接传递完整请求体
            BlockResponse response = difyService.blockingMessageWithRequestBody(
                requestBody,
                calculationApiKey
            );

            if (response == null || response.getAnswer() == null) {
                log.error("Dify API返回空响应");
                return R.fail("AI计算服务暂时不可用");
            }

            // 解析Dify响应
            AuditResult result = parseDifyResponseForInputs(response.getAnswer(), requestBody);

            if (result.isSuccess()) {
                return R.data(result);
            } else {
                return R.fail(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("使用Dify计算审计费用时发生错误", e);
            return R.fail("AI计算错误: " + e.getMessage());
        }
    }

    /**
     * 培训类接口
     */
    @PostMapping(value = "/calculate-training-with-inputs-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "使用Dify AI流式计算审计费用 - inputs格式", description = "使用Dify AI引擎根据培训参数进行流式计算")
    @Parameters({
        @Parameter(name = "requestBody", description = "完整的Dify请求体")
    })
    public Flux<WorkflowStreamResponse> calculateTrainingWithInputsStream(@RequestBody Map<String, Object> requestBody) {
        return Flux.defer(() -> {
            try {
                log.info("开始使用Dify AI流式计算审计费用 (inputs格式): {}", requestBody);

                // 确保response_mode为streaming
                requestBody.put("response_mode", "streaming");

                // 调用Dify流式API - 直接传递完整请求体，返回WorkflowStreamResponse
                return difyService.streamingMessageWithRequestBodyWorkflow(
                    requestBody,
                    calculationApiKey
                );

            } catch (Exception e) {
                log.error("使用Dify流式计算审计费用时发生错误", e);
                // 返回错误格式的WorkflowStreamResponse
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setErrorInfo("参数处理失败: " + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }


    @PostMapping(value = "/calculate-supplier-inquiry-with-inputs-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "供应商询价最高限价编制", description = "使用Dify AI引擎根据培训参数进行流式计算")
    @Parameters({
            @Parameter(name = "requestBody", description = "完整的Dify请求体")
    })
    public Flux<WorkflowStreamResponse> calculateSupplierInquiryWithInputsStream(@RequestBody Map<String, Object> requestBody) {
        return Flux.defer(() -> {
            try {
                log.info("开始使用Dify AI流式计算审计费用 (inputs格式): {}", requestBody);

                // 确保response_mode为streaming
                requestBody.put("response_mode", "streaming");

                // 调用Dify流式API - 直接传递完整请求体，返回WorkflowStreamResponse
                return difyService.streamingMessageWithObjectInputsWorkflow(
                        requestBody,
                        inquiryCalculationApiKey
                );

            } catch (Exception e) {
                log.error("使用Dify流式计算审计费用时发生错误", e);
                // 返回错误格式的WorkflowStreamResponse
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setErrorInfo("参数处理失败: " + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 构建Dify查询内容
     */
    private String buildDifyQuery(AuditParams params) {
        StringBuilder query = new StringBuilder();
        query.append("请根据以下项目信息计算最高限价：\n\n");
        query.append("项目名称：").append(params.getProjectName()).append("\n");
        query.append("服务类型：").append(params.getServiceType()).append("\n");
        query.append("计算方法：").append(params.getCalculationMethod()).append("\n");

        if (params.getTableData() != null && !params.getTableData().isEmpty()) {
            query.append("标准表数据：\n").append(params.getTableData()).append("\n");
        }

        query.append("\n请按照以下格式返回JSON结果：\n");
        query.append("{\n");
        query.append("  \"success\": true,\n");
        query.append("  \"totalCost\": 数值,\n");
        query.append("  \"calculationProcess\": [\"计算步骤1\", \"计算步骤2\", ...],\n");
        query.append("  \"details\": {\"详细信息\": \"值\"}\n");
        query.append("}\n");

        return query.toString();
    }

    /**
     * 解析Dify响应
     */
    private AuditResult parseDifyResponse(String answer, AuditParams params) {
        try {
            // 尝试从响应中提取JSON
            String jsonStr = extractJsonFromAnswer(answer);

            if (jsonStr != null) {
                JSONObject jsonResponse = JSON.parseObject(jsonStr);

                if (jsonResponse.getBooleanValue("success")) {
                    AuditResult result = AuditResult.success(
                        jsonResponse.getBigDecimal("totalCost")
                    );

                    // 设置计算过程
                    if (jsonResponse.containsKey("calculationProcess")) {
                        List<String> process = jsonResponse.getList("calculationProcess", String.class);
                        if (process != null) {
                            result.setCalculationProcess(process);
                        }
                    }

                    return result;
                } else {
                    return AuditResult.fail(jsonResponse.getString("errorMessage"));
                }
            } else {
                // 如果无法解析JSON，创建一个基于文本的结果
                return createFallbackResult(answer, params);
            }

        } catch (Exception e) {
            log.error("解析Dify响应失败", e);
            return createFallbackResult(answer, params);
        }
    }

    /**
     * 流式计算审计费用
     */
    @GetMapping(value = "/calculate-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流式计算审计费用", description = "使用Dify AI流式计算审计费用")
    public Flux<WorkflowStreamResponse> calculateStream(
            @RequestParam String trainingType,
            @RequestParam Integer trainingDays,
            @RequestParam Integer traineeCount,
            @RequestParam Integer dailyHours,
            @RequestParam String instructorTitle,
            @RequestParam String trainingCategory) {

        return Flux.defer(() -> {
            try {
                log.info("开始流式计算审计费用");

                // 构建完整的请求体
                Map<String, Object> requestBody = new HashMap<>();
                Map<String, String> inputs = new HashMap<>();
                inputs.put("training_type", trainingType);
                inputs.put("training_days", String.valueOf(trainingDays));
                inputs.put("trainee_count", String.valueOf(traineeCount));
                inputs.put("daily_hours", String.valueOf(dailyHours));
                inputs.put("instructor_title", instructorTitle);
                inputs.put("training_category", trainingCategory);

                requestBody.put("inputs", inputs);
                requestBody.put("response_mode", "streaming");
                requestBody.put("user", "0");

                // 调用Dify流式API（返回WorkflowStreamResponse）
                return difyService.streamingMessageWithRequestBodyWorkflow(
                    requestBody,
                    calculationApiKey
                );

            } catch (Exception e) {
                log.error("流式计算审计费用时发生错误", e);
                // 返回错误格式的WorkflowStreamResponse
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setErrorInfo("参数处理失败: " + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }


    /**
     * 货物类接口
     */
    @PostMapping(value = "/calculate-goods-with-inputs-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "使用Dify AI流式计算货物类审计费用 - inputs格式", description = "使用Dify AI引擎根据货物参数进行流式计算")
    @Parameters({
        @Parameter(name = "requestBody", description = "完整的Dify请求体")
    })
    public Flux<WorkflowStreamResponse> calculateGoodsWithInputsStream(@RequestBody Map<String, Object> requestBody) {
        return Flux.defer(() -> {
            try {
                log.info("开始使用Dify AI流式计算货物类审计费用 (inputs格式): {}", requestBody);

                // 确保response_mode为streaming
                requestBody.put("response_mode", "streaming");

                // 调用Dify流式API - 使用货物类专用的API Key，支持对象类型inputs
                return difyService.streamingMessageWithObjectInputsWorkflow(
                    requestBody,
                    goodsCalculationApiKey
                );

            } catch (Exception e) {
                log.error("使用Dify流式计算货物类审计费用时发生错误", e);
                // 返回错误格式的WorkflowStreamResponse
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setErrorInfo("参数处理失败: " + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 使用Dify AI流式计算工程类审计费用
     */
    @PostMapping(value = "/calculate-engineering-with-inputs-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "使用Dify AI流式计算工程类审计费用", description = "使用Dify AI引擎根据工程类参数流式计算审计费用")
    public Flux<WorkflowStreamResponse> calculateEngineeringWithInputsStream(@RequestBody Map<String, Object> requestBody) {
        return Flux.defer(() -> {
            try {
                log.info("开始使用Dify AI流式计算工程类审计费用 (inputs格式): {}", requestBody);

                // 确保response_mode为streaming
                requestBody.put("response_mode", "streaming");

                // 调用Dify流式API - 使用工程类专用的API Key
                return difyService.streamingMessageWithRequestBodyWorkflow(
                    requestBody,
                    engineeringCalculationApiKey
                );

            } catch (Exception e) {
                log.error("使用Dify流式计算工程类审计费用时发生错误", e);
                // 返回错误格式的WorkflowStreamResponse
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setErrorInfo("参数处理失败: " + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 从回答中提取JSON字符串
     */
    private String extractJsonFromAnswer(String answer) {
        if (answer == null) return null;

        // 查找JSON开始和结束位置
        int startIndex = answer.indexOf("{");
        int endIndex = answer.lastIndexOf("}");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return answer.substring(startIndex, endIndex + 1);
        }

        return null;
    }

    /**
     * 创建备用结果（当无法解析JSON时）
     */
    private AuditResult createFallbackResult(String answer, AuditParams params) {
        // 尝试从文本中提取数字作为总费用
        BigDecimal totalCost = extractNumberFromText(answer);

        AuditResult result = AuditResult.success(totalCost);

        // 将整个回答作为计算过程
        List<String> process = new ArrayList<>();
        process.add("AI分析结果：");
        process.add(answer);
        result.setCalculationProcess(process);

        return result;
    }

    /**
     * 从文本中提取数字
     */
    private BigDecimal extractNumberFromText(String text) {
        if (text == null) return BigDecimal.ZERO;

        // 使用正则表达式查找数字
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+(?:\\.\\d+)?");
        java.util.regex.Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            try {
                return new BigDecimal(matcher.group());
            } catch (NumberFormatException e) {
                log.warn("无法解析数字: {}", matcher.group());
            }
        }

        return BigDecimal.ZERO;
    }

    /**
     * 解析Dify响应 - 用于inputs格式
     */
    private AuditResult parseDifyResponseForInputs(String answer, Map<String, Object> params) {
        try {
            // 尝试从响应中提取JSON
            String jsonStr = extractJsonFromAnswer(answer);

            if (jsonStr != null) {
                JSONObject jsonResponse = JSON.parseObject(jsonStr);

                if (jsonResponse.getBooleanValue("success")) {
                    AuditResult result = AuditResult.success(
                        jsonResponse.getBigDecimal("totalCost")
                    );

                    // 设置计算过程
                    if (jsonResponse.containsKey("calculationProcess")) {
                        List<String> process = jsonResponse.getList("calculationProcess", String.class);
                        if (process != null) {
                            result.setCalculationProcess(process);
                        }
                    }

                    return result;
                } else {
                    return AuditResult.fail(jsonResponse.getString("errorMessage"));
                }
            } else {
                // 如果无法解析JSON，创建一个基于文本的结果
                return createFallbackResultForInputs(answer, params);
            }

        } catch (Exception e) {
            log.error("解析Dify响应失败", e);
            return createFallbackResultForInputs(answer, params);
        }
    }

    /**
     * 创建备用结果（当无法解析JSON时）- 用于inputs格式
     */
    private AuditResult createFallbackResultForInputs(String answer, Map<String, Object> params) {
        // 尝试从文本中提取数字作为总费用
        BigDecimal totalCost = extractNumberFromText(answer);

        AuditResult result = AuditResult.success(totalCost);

        // 将整个回答作为计算过程
        List<String> process = new ArrayList<>();
        process.add("AI分析结果：");
        process.add(answer);
        result.setCalculationProcess(process);

        return result;
    }
}
