import request from '@/axios';
import { baseUrl } from '@/config/env';
import { getToken } from '@/utils/auth';
import { Base64 } from 'js-base64';
import website from '@/config/website';

// 使用Dify AI计算审计费用 - 原始query格式（兼容旧页面）
export const calculateAudit = (params) => {
  return request({
    url: '/xjzs/audit/calculate',
    method: 'post',
    data: params
  });
};

// 使用Dify AI计算审计费用 - 新的inputs格式（用于新页面）
export const calculateAuditWithInputs = (inputs, responseMode = 'blocking') => {
  // 构建完整的Dify请求体
  const requestBody = {
    inputs: inputs,
    response_mode: responseMode,
    user: 'abc-123'
  };

  return request({
    url: '/xjzs/audit/calculate-with-inputs',
    method: 'post',
    data: requestBody
  });
};

// 供应商询价类接口
export const calculateSupplierInquiryWithInputsStream = (inputs) => {
  // 构建完整的Dify请求体
  const requestBody = {
    inputs: inputs,
    response_mode: 'streaming',
    user: 'abc-123'
  };

  // 构建完整的URL
  const url = `${baseUrl}/xjzs/audit/calculate-supplier-inquiry-with-inputs-stream`;

  // 准备请求头（流式接口已排除权限控制，不需要token）
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Blade-Requested-With': 'BladeHttpRequest'
  };

  // 添加基础认证
  headers['Authorization'] = `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`;

  // 注意：流式接口已在后端排除权限控制，不需要token认证
  // 这样可以避免token过期导致的流式连接中断问题

  // 使用fetch API来处理流式响应
  return fetch(url, {
    method: 'POST',
    headers: headers,
    credentials: 'include', // 包含cookies
    body: JSON.stringify(requestBody)
  });
};

// 培训类接口
export const calculateAuditWithInputsStream = (inputs) => {
  // 构建完整的Dify请求体
  const requestBody = {
    inputs: inputs,
    response_mode: 'streaming',
    user: 'abc-123'
  };

  // 构建完整的URL
  const url = `${baseUrl}/xjzs/audit/calculate-training-with-inputs-stream`;

  // 准备请求头（流式接口已排除权限控制，不需要token）
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Blade-Requested-With': 'BladeHttpRequest'
  };

  // 添加基础认证
  headers['Authorization'] = `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`;

  // 注意：流式接口已在后端排除权限控制，不需要token认证
  // 这样可以避免token过期导致的流式连接中断问题

  // 使用fetch API来处理流式响应
  return fetch(url, {
    method: 'POST',
    headers: headers,
    credentials: 'include', // 包含cookies
    body: JSON.stringify(requestBody)
  });
};

// 使用Dify AI流式计算货物类审计费用 - 货物类专用API
export const calculateGoodsAuditWithInputsStream = (inputs) => {
  // 构建完整的Dify请求体
  const requestBody = {
    inputs: inputs,
    response_mode: 'streaming',
    user: 'abc-123'
  };

  // 构建完整的URL - 货物类专用端点
  const url = `${baseUrl}/xjzs/audit/calculate-goods-with-inputs-stream`;

  // 准备请求头（流式接口已排除权限控制，不需要token）
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Blade-Requested-With': 'BladeHttpRequest'
  };

  // 添加基础认证
  headers['Authorization'] = `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`;

  // 使用fetch API来处理流式响应
  return fetch(url, {
    method: 'POST',
    headers: headers,
    credentials: 'include', // 包含cookies
    body: JSON.stringify(requestBody)
  });
};

// 使用Dify AI流式计算工程类审计费用 - 工程类专用API
export const calculateEngineeringAuditWithInputsStream = (inputs) => {
  // 构建完整的Dify请求体
  const requestBody = {
    inputs: inputs,
    response_mode: 'streaming',
    user: 'abc-123'
  };

  // 构建完整的URL - 工程类专用端点
  const url = `${baseUrl}/xjzs/audit/calculate-engineering-with-inputs-stream`;

  // 准备请求头（流式接口已排除权限控制，不需要token）
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Blade-Requested-With': 'BladeHttpRequest'
  };

  // 添加基础认证
  headers['Authorization'] = `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`;

  // 使用fetch API来处理流式响应
  return fetch(url, {
    method: 'POST',
    headers: headers,
    credentials: 'include', // 包含cookies
    body: JSON.stringify(requestBody)
  });
};