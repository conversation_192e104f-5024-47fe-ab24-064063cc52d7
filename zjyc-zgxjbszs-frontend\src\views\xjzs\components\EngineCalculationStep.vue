<template>
  <div class="step-content">
    <!-- <h3>引擎计算</h3>
    <p class="step-description">智能计算项目最高限价</p> -->

    <div class="calculation-container">
      <div class="project-info">
        <!-- <h4>项目信息</h4>
        <div class="project-info-grid">
          <div class="info-item">
            <span class="info-label">项目名称：</span>
            <span class="info-value">{{ formData.projectName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">项目类型：</span>
            <span class="info-value">{{ formData.projectType }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">算法类别：</span>
            <span class="info-value">{{ formData.algorithmCategory }}</span>
          </div> -->
        <div class="info-item">
          <span class="info-label">计算方式：</span>
          <span class="info-value">{{ getCalculationMethodName(formData.calculationMethod) }}</span>
        </div>
        <!-- </div> -->

      </div>

      <!-- 流式计算显示区域 -->
      <div class="stream-calculation">
        <!-- <div class="stream-header">
            <h4>思维链</h4>
            <div class="stream-status">
              <span v-if="isStreamCalculating" class="status-text calculating">
                <i class="el-icon-loading"></i>
                正在计算...
              </span>
              <span v-else-if="streamContent && streamContent.length > 0" class="status-text completed">
                <i class="el-icon-check"></i>
                计算完成
              </span>
              <span v-else class="status-text waiting">
                <i class="el-icon-time"></i>
                准备中...
              </span>
            </div>
          </div> -->

        <div class="stream-content" ref="streamContentRef">
          <div v-if="!isStreamCalculating && !streamContent" class="stream-waiting">
            <div class="waiting-info">
              <div class="calc-badge">
                <i class="el-icon-cpu"></i>
                <span>智能计算引擎</span>
              </div>
              <p class="calc-description">基于项目参数进行智能分析计算</p>
              <el-button type="primary" @click="startStreamCalculationNew" :loading="isStreamCalculating" size="large">
                <i class="el-icon-video-play"></i>
                开始计算
              </el-button>
            </div>
          </div>
          <div v-else>
            <XMarkdown :markdown="processMarkdown(streamContent) || '等待计算开始...'" class="stream-text" :options="{
              html: true,
              breaks: true,
              linkify: true,
              typographer: true,
              langPrefix: 'language-',  // 添加语言前缀，帮助识别json语言
              highlight: function (str, lang) {
                // 确保所有代码块都能被正确高亮，特别是json
                return '';  // 返回空字符串让组件使用默认高亮
              }
            }" />
            <div v-if="isStreamCalculating" class="stream-cursor">|</div>
          </div>
        </div>
      </div>

<!--      <div v-if="calculationResult && !streamContent" class="result">-->
<!--        <div class="result-header">-->
<!--          <h4>计算结果</h4>-->
<!--          <el-tag type="success" v-if="calculationResult.success">计算成功</el-tag>-->
<!--          <el-tag type="danger" v-else>计算失败</el-tag>-->
<!--        </div>-->

<!--        <div v-if="calculationResult.success" class="result-content">-->
<!--          <div class="price-box">-->
<!--            <div class="price-label">最高限价</div>-->
<!--            <div class="price-value">¥ {{ formatPrice(calculationResult.totalCost) }}</div>-->
<!--          </div>-->

<!--          &lt;!&ndash; 计算过程展示 &ndash;&gt;-->
<!--          <div class="calculation-thinking"-->
<!--            v-if="calculationResult.calculationProcess && calculationResult.calculationProcess.length > 0">-->
<!--            <h5>计算思路</h5>-->
<!--            <div class="thinking-console">-->
<!--              <div v-for="(step, index) in calculationResult.calculationProcess" :key="index" class="console-line">-->
<!--                <span class="timestamp">[{{ formatTimestamp(index) }}]</span> {{ step }}-->
<!--              </div>-->
<!--              <div class="console-cursor">></div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->

<!--        <div v-else class="error-message">-->
<!--          <el-alert :title="calculationResult.errorMessage" type="error" :closable="false" show-icon>-->
<!--          </el-alert>-->
<!--        </div>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script>
import { ref, nextTick, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { calculateAuditWithInputsStream, calculateGoodsAuditWithInputsStream, calculateEngineeringAuditWithInputsStream, calculateSupplierInquiryWithInputsStream} from '@/api/xjzs/audit';
import MathRenderer from '@/components/MathRenderer.vue';
import { XMarkdown } from 'vue-element-plus-x';

export default {
  name: 'EngineCalculationStep',
  components: {
    MathRenderer,
    XMarkdown
  },
  props: {
    formData: {
      type: Object,
      required: true
    },
    isDetailMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {
    const calculationResult = ref(null);

    // 流式计算相关状态
    const isStreamCalculating = ref(false);
    const streamContent = ref('');
    const streamContentRef = ref(null);
    const eventSource = ref(null);

    // 代码块缓冲状态
    const codeBlockBuffer = ref('');
    const isInCodeBlock = ref(false);

    // 获取计算方式的中文名称
    const getCalculationMethodName = (method) => {
      const methodNames = {
        'government': '政府、行业指导价格法',
        // 'cost': '成本核算法',
        // 'historical': '历史价格法',
        'market': '市场调研法',
        // 'comprehensive': '综合定价法'
        'supplierInquiry': '供应商询价'
      };
      return methodNames[method] || '未选择';
    };

    // 格式化价格
    const formatPrice = (price) => {
      if (!price) return '0.00';
      return parseFloat(price).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };

    // 格式化时间戳
    const formatTimestamp = (index) => {
      // 模拟时间戳格式 HH:MM:SS
      const baseTime = new Date();
      baseTime.setHours(15);
      baseTime.setMinutes(37);
      baseTime.setSeconds(29 + index);

      const hours = baseTime.getHours().toString().padStart(2, '0');
      const minutes = baseTime.getMinutes().toString().padStart(2, '0');
      const seconds = baseTime.getSeconds().toString().padStart(2, '0');

      return `${hours}:${minutes}:${seconds}`;
    };

    // 简化的流式文本处理函数
    const processStreamText = (newText) => {
      if (!newText) return streamContent.value;

      // 对于流式处理，直接返回新文本，不做复杂的代码块处理
      // 因为流式内容通常是完整的，不需要特殊的缓冲处理
      return newText;
    };

    // 修复代码块完整性的函数
    const fixCodeBlocks = (text) => {
      if (!text) return '';

      // 只在最终结果处理时才修复代码块，流式处理时不修复
      if (isStreamCalculating.value) {
        return text;
      }

      // 统计```的数量
      const codeBlockMarkers = text.match(/```/g);
      if (!codeBlockMarkers) return text;

      // 如果```数量为奇数，说明有未闭合的代码块
      if (codeBlockMarkers.length % 2 !== 0) {
        console.warn('检测到未闭合的代码块，自动添加结束标记');
        return text + '\n```';
      }

      return text;
    };

    // Markdown处理函数，增强对JSON代码块的支持
    const processMarkdown = (text) => {
      if (!text) return '';

      // 首先修复代码块完整性
      let processed = fixCodeBlocks(text);

      // 特别处理JSON代码块，确保格式正确
      // 使用非贪婪匹配，避免匹配到多个代码块
      const jsonCodeBlockRegex = /```json\s*([\s\S]*?)```/g;
      
      // 在流式处理时，不尝试解析和格式化JSON内容
      if (!isStreamCalculating.value) {
        processed = processed.replace(jsonCodeBlockRegex, (match, jsonContent) => {
          // 直接返回原始内容，不尝试解析，避免格式化问题
          return match;
        });
      }

      // 只做基本的空行优化，不破坏代码块结构
      processed = processed.replace(/\n{4,}/g, '\n\n\n');

      return processed;
    };



    // 从文本中提取数字的辅助函数
    const extractNumberFromText = (text) => {
      if (!text) return 0;

      // 优先匹配表格中的合计行，寻找最后一个数字（通常是总金额）
      const tableMatch = text.match(/合计[\s\S]*?([0-9,]+\.?[0-9]*)\s*$/m);
      if (tableMatch && tableMatch[1]) {
        const numberStr = tableMatch[1].replace(/,/g, '');
        const number = parseFloat(numberStr);
        if (!isNaN(number) && number > 0) {
          return number;
        }
      }

      // 尝试匹配各种数字格式
      const patterns = [
        /总费用[：:]\s*([0-9,]+\.?[0-9]*)/i,
        /最高限价[：:]\s*([0-9,]+\.?[0-9]*)/i,
        /总计[：:]\s*([0-9,]+\.?[0-9]*)/i,
        /合计[：:\s]+([0-9,]+\.?[0-9]*)/i,
        // 匹配表格最后一行的金额
        /\|\s*([0-9,]+\.?[0-9]*)\s*\|\s*$/m,
        /¥\s*([0-9,]+\.?[0-9]*)/,
        /([0-9,]+\.?[0-9]*)\s*元/
      ];

      // 收集所有匹配的数字，选择最大的（通常是总计）
      let maxNumber = 0;
      for (const pattern of patterns) {
        const matches = text.matchAll(new RegExp(pattern.source, pattern.flags + 'g'));
        for (const match of matches) {
          if (match[1]) {
            const numberStr = match[1].replace(/,/g, '');
            const number = parseFloat(numberStr);
            if (!isNaN(number) && number > maxNumber) {
              maxNumber = number;
            }
          }
        }
      }

      return maxNumber;
    };

    // 解析采购类AI回复中的价格信息，并结合第一步的数量信息
    const parseProcurementPrices = (content) => {
      const priceResults = [];
      const processedItems = new Set(); // 用于去重
      let totalCost = 0;

      try {
        // 获取第一步的京东慧采表格数据
        const jdTableRows = props.formData.jdTableRows || [];
        console.log('🔍 第一步京东慧采数据:', jdTableRows);

        // 查找所有JSON格式的价格信息，如 {鼠标垫: 46.0} 或 {"条码扫描": 255.05}
        const jsonMatches = content.match(/\{[^}]+\}/g);

        if (jsonMatches && jsonMatches.length > 0) {
          console.log('🔍 找到JSON格式价格信息:', jsonMatches);

          jsonMatches.forEach((jsonStr) => {
            try {
              // 尝试解析JSON
              const priceObj = JSON.parse(jsonStr);

              // 遍历价格对象的每个键值对
              Object.entries(priceObj).forEach(([itemName, price]) => {
                const numericPrice = parseFloat(price);
                if (!isNaN(numericPrice) && numericPrice > 0) {

                  // 检查是否已经处理过这个商品
                  if (processedItems.has(itemName)) {
                    console.log(`🔍 商品 ${itemName} 已处理过，跳过重复项`);
                    return;
                  }

                  // 标记为已处理
                  processedItems.add(itemName);

                  // 查找第一步中对应的商品信息 - 使用更精确的匹配逻辑
                  const matchingItem = jdTableRows.find(row => {
                    if (!row.feeName) return false;

                    // 精确匹配优先
                    if (row.feeName === itemName) return true;

                    // 包含匹配（双向）
                    if (row.feeName.includes(itemName) || itemName.includes(row.feeName)) {
                      return true;
                    }

                    // 去除空格后匹配
                    const cleanRowName = row.feeName.replace(/\s+/g, '');
                    const cleanItemName = itemName.replace(/\s+/g, '');
                    if (cleanRowName === cleanItemName) return true;

                    return false;
                  });

                  let quantity = 1;
                  let unit = '个';
                  let spec = '-';

                  if (matchingItem) {
                    quantity = matchingItem.quantity || 1;
                    unit = matchingItem.unit || '个';
                    spec = matchingItem.attributes && matchingItem.attributes.length > 0
                      ? matchingItem.attributes.join(', ')
                      : '-';
                    console.log(`🔍 找到匹配商品: ${itemName}, 数量: ${quantity}, 单位: ${unit}`);
                  } else {
                    console.log(`🔍 未找到匹配商品: ${itemName}, 使用默认数量: ${quantity}`);
                  }

                  const totalPrice = numericPrice * quantity;

                  priceResults.push({
                    name: itemName,
                    price: numericPrice,
                    spec: spec,
                    unit: unit,
                    quantity: quantity,
                    unitPrice: numericPrice,
                    totalPrice: totalPrice
                  });

                  totalCost += totalPrice;
                  console.log(`🔍 解析到价格: ${itemName} = 单价${numericPrice} × 数量${quantity} = 总价${totalPrice}`);
                }
              });
            } catch (parseError) {
              console.warn('🔍 解析JSON失败:', jsonStr, parseError);
            }
          });
        }

        // 如果没有找到JSON格式，尝试其他格式
        if (priceResults.length === 0) {
          console.log('🔍 未找到JSON格式，尝试其他格式解析');
          const extractedCost = extractNumberFromText(content);
          if (extractedCost > 0) {
            totalCost = extractedCost;
          }
        }

      } catch (error) {
        console.error('🔍 解析采购类价格信息失败:', error);
      }

      return { priceResults, totalCost };
    };

    // 解析工程咨询类计算结果
    const parseEngineeringResults = (content) => {
      console.log('🔍 开始解析工程咨询类计算结果');
      console.log('🔍 解析内容预览:', content.substring(0, 500));
      let totalCost = 0;
      let engineeringResults = [];

      try {
        // 尝试从文本中提取最终计算金额
        // 1. 匹配"最终答案"格式：**预算、结算审核的最高限价为 392.85 万元**
        const finalAnswerPattern = content.match(/\*\*[^*]*?最高限价为\s*([0-9,]+\.?[0-9]*)\s*万元\*\*/i);
        if (finalAnswerPattern && finalAnswerPattern[1]) {
          const numberStr = finalAnswerPattern[1].replace(/,/g, '');
          totalCost = parseFloat(numberStr) * 10000; // 万元转换为元
          console.log('🔍 从"最终答案"格式提取到金额:', totalCost, '(原值:', finalAnswerPattern[1], '万元)');
        }

        // 2. 匹配"最终结果"格式：**工程项目监理的最高限价为：809.0万元**
        if (totalCost === 0 || isNaN(totalCost)) {
          const finalResultPattern1 = content.match(/\*\*[^*]*?最高限价为[：:]\s*([0-9,]+\.?[0-9]*)\s*万元\*\*/i);
          if (finalResultPattern1 && finalResultPattern1[1]) {
            const numberStr = finalResultPattern1[1].replace(/,/g, '');
            totalCost = parseFloat(numberStr) * 10000; // 万元转换为元
            console.log('🔍 从"最终结果"格式1提取到金额:', totalCost, '(原值:', finalResultPattern1[1], '万元)');
          }
        }

        // 3. 匹配其他"最终结果/答案"格式
        if (totalCost === 0 || isNaN(totalCost)) {
          const finalResultPattern2 = content.match(/(?:最终结果|最终答案)[^\d]*?([0-9,]+\.?[0-9]*)\s*万元/i);
          if (finalResultPattern2 && finalResultPattern2[1]) {
            const numberStr = finalResultPattern2[1].replace(/,/g, '');
            totalCost = parseFloat(numberStr) * 10000; // 万元转换为元
            console.log('🔍 从"最终结果/答案"格式2提取到金额:', totalCost, '(原值:', finalResultPattern2[1], '万元)');
          }
        }

        // 3. 匹配"最终计算结果"或"最终费用"或"咨询费"后面的金额（元）
        if (totalCost === 0 || isNaN(totalCost)) {
          const finalResultMatch = content.match(/(?:最终计算结果|最终费用|咨询费|工程造价咨询费)[^\d]*?([0-9,]+\.?[0-9]*)\s*元/i);
          if (finalResultMatch && finalResultMatch[1]) {
            const numberStr = finalResultMatch[1].replace(/,/g, '');
            totalCost = parseFloat(numberStr);
            console.log('🔍 从"最终计算结果"提取到金额:', totalCost);
          }
        }

        // 4. 匹配"最终计算结果"或"最终费用"或"咨询费"后面的金额（万元）
        if (totalCost === 0 || isNaN(totalCost)) {
          const finalResultMatchWan = content.match(/(?:最终计算结果|最终费用|咨询费|工程造价咨询费)[^\d]*?([0-9,]+\.?[0-9]*)\s*万元/i);
          if (finalResultMatchWan && finalResultMatchWan[1]) {
            const numberStr = finalResultMatchWan[1].replace(/,/g, '');
            totalCost = parseFloat(numberStr) * 10000; // 万元转换为元
            console.log('🔍 从"最终计算结果"(万元)提取到金额:', totalCost, '(原值:', finalResultMatchWan[1], '万元)');
          }
        }

        // 5. 如果上面没有匹配到，尝试匹配表格中的合计行
        if (totalCost === 0 || isNaN(totalCost)) {
          const tableMatch = content.match(/合计[^\d]*?([0-9,]+\.?[0-9]*)\s*元/);
          if (tableMatch && tableMatch[1]) {
            const numberStr = tableMatch[1].replace(/,/g, '');
            totalCost = parseFloat(numberStr);
            console.log('🔍 从表格合计行提取到金额:', totalCost);
          }
        }

        // 6. 如果还是没有匹配到，使用通用提取方法
        if (totalCost === 0 || isNaN(totalCost)) {
          totalCost = extractNumberFromText(content);
          console.log('🔍 使用通用方法提取到金额:', totalCost);
        }

        // 尝试提取工程咨询类的详细计算过程，构建表格数据
        // 这里可以根据实际的Dify输出格式进行调整

        // 尝试从内容中提取工程咨询类型（预算审核、结算审核等）
        let consultingType = '竣工决算审计'; // 默认值
        let workResult = '工程造价咨询服务'; // 默认值

        // 尝试从内容中提取咨询类型
        const typeMatch = content.match(/\*\*([^*]*?)的最高限价为/i);
        if (typeMatch && typeMatch[1]) {
          consultingType = typeMatch[1].trim();
          // 去除开头的冒号（如果存在）
          if (consultingType.startsWith('：') || consultingType.startsWith(':')) {
            consultingType = consultingType.substring(1).trim();
          }
          console.log('🔍 从内容中提取到咨询类型:', consultingType);
        }

        // 创建一个工程咨询费用项
        engineeringResults = [{
          id: 1,
          category: consultingType, // 直接使用字符串，不用数组包装
          workResult: workResult,
          complexityFactor: '0.85', // 默认复杂系数
          baseFee: totalCost, // 使用提取的总费用作为基础费用
          fee: totalCost // 总费用
        }];

        console.log('🔍 创建的工程咨询费用项:', engineeringResults);
      } catch (error) {
        console.error('🔍 解析工程咨询类计算结果失败:', error);
      }

      return { engineeringResults, totalCost };
    };

    // 解析流式结果
    const parseStreamResult = () => {
      if (!streamContent.value) {
        ElMessage.warning('没有可解析的内容');
        return;
      }

      console.log('🔍 开始解析流式结果');
      console.log('🔍 内容长度:', streamContent.value.length);
      console.log('🔍 项目类型:', props.formData.projectType);

      // 清空之前的标准表格数据，避免累积重复
      if (props.formData.projectType === '货物类' || props.formData.projectType === '货物') {
        props.formData.standardTableRows = [];
        console.log('🔍 清空之前的标准表格数据');
      }

      let extractedCost = 0;
      let tableRows = [];

      // 根据项目类型选择不同的解析策略
      if (props.formData.projectType === '货物类' || props.formData.projectType === '货物') {
        // 采购类/货物类：解析JSON格式的价格信息
        const { priceResults, totalCost } = parseProcurementPrices(streamContent.value);

        if (priceResults.length > 0) {
          console.log('🔍 采购类解析结果:', priceResults);

          // 更新标准表格数据
          tableRows = priceResults.map((item, index) => ({
            id: index + 1,
            feeName: item.name,
            specification: item.spec,
            unit: item.unit,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice
          }));

          // 更新formData中的标准表格数据
          props.formData.standardTableRows = tableRows;
          console.log('🔍 更新标准表格数据:', tableRows);

          extractedCost = totalCost;
        } else {
          // 如果没有解析到价格信息，使用传统方法
          extractedCost = extractNumberFromText(streamContent.value);
        }
      } else if (props.formData.projectType === '工程咨询类' || props.formData.projectType === '工程') {
        // 工程咨询类：使用专门的解析方法
        console.log('🔍 使用工程咨询类解析方法');
        const { engineeringResults, totalCost } = parseEngineeringResults(streamContent.value);

        console.log('🔍 工程咨询类解析结果 - 金额:', totalCost);
        console.log('🔍 工程咨询类解析结果 - 表格数据:', engineeringResults);

        // 无论是否有表格数据，都要更新金额
        extractedCost = totalCost;

        // 如果有解析到的表格数据，更新表格
        if (engineeringResults.length > 0) {
          // 保留原有的表单数据，合并计算结果
          if (props.formData.engineeringTableRows && props.formData.engineeringTableRows.length > 0) {
            // 合并现有数据和计算结果
            props.formData.engineeringTableRows = props.formData.engineeringTableRows.map((existingRow, index) => {
              const calculationRow = engineeringResults[index];
              if (calculationRow) {
                // 合并数据：保留表单输入数据，添加计算结果数据
                return {
                  ...existingRow,        // 保留原有的表单数据（category, constructionCost 等）
                  ...calculationRow,     // 添加计算结果数据（baseFee, fee, workResult 等）
                  // 如果原有数据中的 category 是数组，保留它
                  category: existingRow.category || calculationRow.category
                };
              }
              return existingRow;
            });

            // 如果计算结果比现有数据多，添加额外的计算结果
            if (engineeringResults.length > props.formData.engineeringTableRows.length) {
              const additionalResults = engineeringResults.slice(props.formData.engineeringTableRows.length);
              props.formData.engineeringTableRows.push(...additionalResults);
            }
          } else {
            // 如果没有现有数据，直接使用计算结果
            props.formData.engineeringTableRows = engineeringResults;
          }
          console.log('🔍 已更新工程咨询类表格数据:', props.formData.engineeringTableRows);
        } else {
          // 如果没有解析到表格数据，但有金额，更新现有数据的计算结果字段
          if (totalCost > 0) {
            if (props.formData.engineeringTableRows && props.formData.engineeringTableRows.length > 0) {
              // 更新现有数据的计算结果字段
              props.formData.engineeringTableRows = props.formData.engineeringTableRows.map((existingRow, index) => {
                return {
                  ...existingRow,  // 保留原有的表单数据
                  id: index + 1,
                  workResult: '工程造价咨询服务',
                  complexityFactor: '0.85',
                  baseFee: totalCost,
                  fee: totalCost
                };
              });
            } else {
              // 如果没有现有数据，创建默认数据
              const defaultEngineering = [{
                id: 1,
                category: '工程造价咨询', // 直接使用字符串
                workResult: '工程造价咨询服务',
                complexityFactor: '0.85',
                baseFee: totalCost,
                fee: totalCost
              }];
              props.formData.engineeringTableRows = defaultEngineering;
            }
            console.log('🔍 已更新工程咨询类表格数据（使用总金额）:', props.formData.engineeringTableRows);
          }
        }
      } else {
        // 其他类型：使用传统的数字提取方法
        console.log('🔍 使用传统数字提取方法');
        extractedCost = extractNumberFromText(streamContent.value);
      }

      console.log('🔍 最终提取的金额:', extractedCost);
      console.log('🔍 项目类型:', props.formData.projectType);
      console.log('🔍 算法类别:', props.formData.algorithmCategory);

      // 创建计算结果
      const result = {
        success: true,
        totalCost: extractedCost,
        calculationProcess: [streamContent.value],
        details: {
          tableRows: tableRows
        }
      };

      calculationResult.value = result;
      props.formData.calculationResult = result;

      console.log('🔍 设置计算结果:', result);
      console.log('🔍 formData.calculationResult:', props.formData.calculationResult);

      // 显示解析结果
      if (props.formData.projectType === '工程咨询类' || props.formData.projectType === '工程') {
        ElMessage.success(`工程咨询类计算完成，总金额：¥${extractedCost.toFixed(2)}`);
      } else if (tableRows.length > 0) {
        ElMessage.success(`成功解析 ${tableRows.length} 个商品的价格信息，总金额：¥${extractedCost.toFixed(2)}`);
      } else {
        ElMessage.success(`计算完成，总金额：¥${extractedCost.toFixed(2)}`);
      }

      // 计算完成后自动跳转到第三步
      setTimeout(() => {
        console.log('🔍 准备跳转到第三步');
        console.log('🔍 跳转前的formData.calculationResult:', props.formData.calculationResult);
        emit('next-step');
      }, 1000); // 延迟1秒让用户看到计算完成的消息
    };

    // 清除流式内容
    const clearStreamContent = () => {
      streamContent.value = '';
      calculationResult.value = null;
      codeBlockBuffer.value = '';
      isInCodeBlock.value = false;
      if (eventSource.value) {
        eventSource.value.close();
        eventSource.value = null;
      }
      isStreamCalculating.value = false;
    };

    // 流式计算方法
    const startStreamCalculationNew = async () => {
      if (isStreamCalculating.value) return;

      try {
        isStreamCalculating.value = true;
        streamContent.value = '';
        calculationResult.value = null;

        // 根据项目类型确定使用的API和参数
        let inputs = {};
        let apiFunction = null;

        if(props.formData.projectStatus == 1) {
          // 供应商询价
          inputs = {
            inquiry_data: props.formData.inquiryRecordRows
          };
          apiFunction = calculateSupplierInquiryWithInputsStream;
        } else if (props.formData.projectType === '培训类') {
          console.log('🔍 trainingTableRows:', props.formData.trainingTableRows);

          // 如果有培训类表格数据，使用第一行数据
          if (props.formData.trainingTableRows && props.formData.trainingTableRows.length > 0) {
            const firstRow = props.formData.trainingTableRows[0];
            console.log('🔍 使用的培训数据行:', firstRow);

            inputs = {
              training_type: firstRow.training_type,
              training_days: firstRow.training_days,
              trainee_count: firstRow.trainee_count,
              daily_hours: firstRow.daily_hours,
              instructor_title: firstRow.instructor_title,
              training_category: firstRow.training_category
            };
          } else {
            console.log('⚠️ 没有找到培训类表格数据，使用默认参数');
          }

          apiFunction = calculateAuditWithInputsStream;
        } else if (props.formData.projectType === '货物类' || props.formData.projectType === '货物') {
          console.log('🔍 jdTableRows:', props.formData.jdTableRows);

          // 货物类默认参数
          inputs = {
            goods_data: '[]'
          };

          // 如果有京东慧采表格数据，直接传递数组对象给dify
          if (props.formData.jdTableRows && props.formData.jdTableRows.length > 0) {
            console.log('🔍 使用的京东慧采数据:', props.formData.jdTableRows);

            // 直接传递数组对象，无需转换为JSON字符串
            inputs = {
              goods_data: props.formData.jdTableRows
            };
          } else {
            console.log('⚠️ 没有找到京东慧采表格数据，使用默认参数');
          }

          apiFunction = calculateGoodsAuditWithInputsStream;
        } else if (props.formData.projectType === '工程咨询类' || props.formData.projectType === '工程') {
          console.log('🔍 engineeringTableRows:', props.formData.engineeringTableRows);

          // 工程类默认参数（初始值为0，用户可选择性填写）
          inputs = {
            ConsultingType: '',
            BuildInstallCost: 0,
            TotalInvestment: 0,
            LandGovFees: 0,
            AppraisedValue: 0,
            Bill_Architectural: 0,
            Bill_Equipment: 0,
            Bill_TrialRun: 0,
            ProfFactor: 0,
            ComplexityFactor: 0,
            ElevationFactor: 0,
            AdditionalFactor: 0,
            OtherDesignFee: 0
          };

          // 如果有工程类表格数据，使用第一行数据
          if (props.formData.engineeringTableRows && props.formData.engineeringTableRows.length > 0) {
            const firstRow = props.formData.engineeringTableRows[0];
            console.log('🔍 使用的工程数据行:', firstRow);

            // 映射ProjectSelectStep中的字段到dify需要的字段
            inputs = {
              // 只使用叶子节点的值，如果是数组则取最后一个元素
              ConsultingType: firstRow.category && firstRow.category.length > 0
                ? (Array.isArray(firstRow.category) ? firstRow.category[firstRow.category.length - 1] : firstRow.category)
                : inputs.ConsultingType,
              BuildInstallCost: parseFloat(firstRow.constructionCost) || inputs.BuildInstallCost,
              TotalInvestment: parseFloat(firstRow.totalInvestment) || inputs.TotalInvestment,
              LandGovFees: parseFloat(firstRow.landAndGovFees) || inputs.LandGovFees,
              AppraisedValue: parseFloat(firstRow.evaluationValue) || inputs.AppraisedValue,
              Bill_Architectural: parseFloat(firstRow.billingAmount?.constructionFee) || inputs.Bill_Architectural,
              Bill_Equipment: parseFloat(firstRow.billingAmount?.equipmentFee) || inputs.Bill_Equipment,
              Bill_TrialRun: parseFloat(firstRow.billingAmount?.testRunFee) || inputs.Bill_TrialRun,
              ProfFactor: parseFloat(firstRow.professionalAdjustment) || inputs.ProfFactor,
              ComplexityFactor: parseFloat(firstRow.complexityAdjustment) || inputs.ComplexityFactor,
              ElevationFactor: parseFloat(firstRow.altitudeAdjustment) || inputs.ElevationFactor,
              AdditionalFactor: parseFloat(firstRow.additionalAdjustment) || inputs.AdditionalFactor,
              OtherDesignFee: parseFloat(firstRow.otherDesignFees) || inputs.OtherDesignFee
            };
          } else {
            console.log('⚠️ 没有找到工程类表格数据，使用默认参数');
          }

          apiFunction = calculateEngineeringAuditWithInputsStream;
        } else {
          throw new Error(`不支持的项目类型: ${props.formData.projectType}`);
        }

        console.log('🔍 最终使用的计算参数:', inputs);
        console.log('开始流式计算，参数:', inputs);

        // 设置超时定时器
        const timeoutId = setTimeout(() => {
          if (isStreamCalculating.value) {
            console.warn('流式计算超时，自动停止');
            isStreamCalculating.value = false;
            ElMessage.warning('AI计算超时，请重试');
          }
        }, 60000); // 60秒超时

        // 调用对应的流式API
        const response = await apiFunction(inputs);

        // 清除超时定时器
        clearTimeout(timeoutId);

        // 检查响应状态
        if (!response.ok) {
          const errorText = await response.text();
          console.error('流式API响应错误:', response.status, errorText);
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        console.log('流式API响应成功，开始处理数据流');

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = ''; // 添加缓冲区来处理跨块的字符
        let lastDataTime = Date.now(); // 记录最后接收数据的时间

        // 添加数据接收监控
        const dataTimeoutId = setInterval(() => {
          if (Date.now() - lastDataTime > 30000) { // 30秒没有数据
            console.warn('30秒内没有接收到数据，可能连接中断');
            clearInterval(dataTimeoutId);
            if (isStreamCalculating.value) {
              isStreamCalculating.value = false;
              ElMessage.warning('连接可能中断，请重试');
            }
          }
        }, 5000);

        while (true) {
          const { done, value } = await reader.read();

          if (value) {
            lastDataTime = Date.now(); // 更新最后接收数据时间
          }

          if (done) {
            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              const remainingLines = buffer.split('\n');
              for (const line of remainingLines) {
                if (line.trim()) {
                  await processStreamLine(line);
                }
              }
            }
            isStreamCalculating.value = false;
            ElMessage.success('AI流式分析完成');

            // 计算完成后自动跳转到第三步
            setTimeout(() => {
              emit('next-step');
            }, 1500); // 延迟1.5秒让用户看到完成消息
            break;
          }

          // 解码数据，使用 stream: true 选项来正确处理跨块的字符
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk; // 将新数据添加到缓冲区

          // 按行分割，但保留最后一个可能不完整的行
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保存最后一行（可能不完整）到缓冲区

          // 处理完整的行
          for (const line of lines) {
            if (line.trim()) {
              await processStreamLine(line);
            }
          }
        }

        // 处理单行数据的函数
        async function processStreamLine(line) {
          let jsonStr = '';

          // 处理不同的数据格式
          if (line.startsWith('data: ')) {
            jsonStr = line.substring(6).trim();
          } else if (line.startsWith('data:')) {
            // 处理没有空格的 data: 格式
            jsonStr = line.substring(5).trim();
          } else if (line.trim().startsWith('{') && line.trim().endsWith('}')) {
            // 直接是JSON格式
            jsonStr = line.trim();
          } else if (line.trim() === '' || line.trim() === 'data:' || line.trim() === 'data: ') {
            // 跳过空行或只有data:的行
            return;
          } else {
            // 其他格式，跳过
            return;
          }

          // 跳过空的JSON字符串
          if (!jsonStr || jsonStr === '') {
            return;
          }

          try {
            const data = JSON.parse(jsonStr);

            // 检查是否是错误事件
            if (data.event === 'error') {
              console.error('服务端返回错误:', data.error || data.description || '未知错误');
              ElMessage.error(data.error || data.description || '服务端处理失败');
              isStreamCalculating.value = false;
              return; // 退出流式处理
            }

            // 处理WorkflowStreamResponse对象结构
            let textContent = '';

            // 根据事件类型处理
            switch (data.event) {
              case 'workflow_started':
                console.log('🚀 工作流开始:', data.data?.id);
                streamContent.value = processMarkdown(streamContent.value + '🚀 开始计算...\n\n');
                break;

              case 'node_started':
                console.log('📝 节点开始:', data.data?.title, data.data?.node_type);
                if (data.data?.node_type === 'llm') {
                  streamContent.value = processMarkdown(streamContent.value + '🤖 正在分析中...\n\n');
                }
                break;

              case 'node_finished':
                // 对于LLM节点完成，显示进度信息，但不处理具体内容
                // 具体内容将在workflow_finished事件中统一处理
                if (data.data?.node_type === 'llm') {
                  console.log('📝 LLM节点完成:', data.data?.title);
                  // 可以在这里添加进度提示，但不处理具体的输出内容
                  // 因为最终的完整结果会在workflow_finished事件中处理
                }
                break;

              case 'text_chunk':
                // 文本块事件，包含实时生成的文本片段
                // 对于服务类计算，这里会有实时内容；对于货物类计算，这里通常为空
                if (data.answer && data.answer.trim()) {
                  textContent = data.answer;
                } else if (data.data && data.data.text && data.data.text.trim()) {
                  textContent = data.data.text;
                }

                // 对于实时流式内容（服务类），直接添加，不做重复检查
                // 因为服务类计算是逐步生成的，每个chunk都是新内容
                break;

              case 'workflow_finished':
                // 工作流完成，使用后端处理好的最终结果
                console.log('🏁 工作流完成，最终内容长度:', streamContent.value.length);
                console.log('🏁 最终内容预览:', streamContent.value.substring(0, 200) + '...');
                console.log('🏁 完整的data对象:', JSON.stringify(data, null, 2));

                // 尝试多种方式获取最终结果
                let finalAnswer = '';

                // 方式1：通过后端的getAnswer方法（data.answer字段）
                if (data.answer && data.answer.trim()) {
                  finalAnswer = data.answer.trim();
                  console.log('🏁 通过data.answer获取到内容，长度:', finalAnswer.length);
                }

                // 方式2：直接从data.data.outputs中获取
                if (!finalAnswer && data.data && data.data.outputs) {
                  console.log('🏁 尝试从data.data.outputs获取内容:', data.data.outputs);

                  if (data.data.outputs.output && Array.isArray(data.data.outputs.output)) {
                    // 如果是数组，检查是否是货物类计算（需要分隔符）还是服务类计算（不需要分隔符）
                    const outputArray = data.data.outputs.output;
                    const results = [];

                    // 检查内容特征来判断计算类型
                    let isGoodsCalculation = false;
                    for (let i = 0; i < outputArray.length; i++) {
                      if (outputArray[i] && outputArray[i].includes('<think>')) {
                        // 包含思维链的是货物类计算
                        isGoodsCalculation = true;
                        break;
                      }
                    }

                    for (let i = 0; i < outputArray.length; i++) {
                      if (outputArray[i] && outputArray[i].trim()) {
                        results.push(outputArray[i].trim());
                      }
                    }

                    if (results.length > 0) {
                      if (isGoodsCalculation && results.length > 1) {
                        // 货物类计算：多个结果用分隔符连接
                        finalAnswer = results.join('\n\n' + '='.repeat(50) + '\n\n');
                        console.log('🏁 货物类计算，从output数组获取到内容，长度:', finalAnswer.length);
                      } else {
                        // 服务类计算：直接连接，不加分隔符
                        finalAnswer = results.join('');
                        console.log('🏁 服务类计算，从output数组获取到内容，长度:', finalAnswer.length);
                      }
                    }
                  } else if (typeof data.data.outputs === 'string') {
                    finalAnswer = data.data.outputs.trim();
                    console.log('🏁 从outputs字符串获取到内容，长度:', finalAnswer.length);
                  }
                }

                // 如果获取到了最终结果，显示它
                if (finalAnswer) {
                  // 检查是否包含JSON代码块
                  const hasJsonBlocks = finalAnswer.includes('```json');
                  
                  if (hasJsonBlocks) {
                    console.log('🏁 检测到JSON代码块，进行特殊处理');
                    
                    // 确保JSON代码块格式正确
                    // 1. 先将所有可能的未闭合代码块闭合
                    let processedAnswer = finalAnswer;
                    const openJsonBlocks = (processedAnswer.match(/```json/g) || []).length;
                    const closeBlocks = (processedAnswer.match(/```\s*$/gm) || []).length;
                    
                    if (openJsonBlocks > closeBlocks) {
                      console.log('🏁 检测到未闭合的JSON代码块，添加闭合标记');
                      processedAnswer += '\n```';
                    }
                    
                    // 2. 设置处理后的内容
                    isStreamCalculating.value = false; // 先设置为false，避免processMarkdown跳过处理
                    codeBlockBuffer.value = '';
                    streamContent.value = processedAnswer;
                    console.log('🏁 JSON代码块处理完成');
                  } else {
                    // 普通内容，直接设置
                    codeBlockBuffer.value = '';
                    streamContent.value = finalAnswer;
                  }
                  
                  console.log('🏁 设置最终内容成功，长度:', streamContent.value.length);

                  // 自动滚动到底部
                  nextTick(() => {
                    if (streamContentRef.value) {
                      streamContentRef.value.scrollTop = streamContentRef.value.scrollHeight;
                    }
                  });
                } else {
                  console.log('🏁 未能获取到最终结果内容');
                }

                setTimeout(() => {
                  isStreamCalculating.value = false;
                  ElMessage.success('计算完成');
                  // 自动解析流式结果
                  parseStreamResult();
                }, 500);
                break;

              case 'error':
                console.error('❌ 工作流错误:', data.error || data.description);
                textContent = `\n❌ 错误: ${data.error || data.description}\n`;
                isStreamCalculating.value = false;
                break;

              default:
                // 对于未知事件，如果有answer字段，也添加到内容中
                if (data.answer && data.answer.trim()) {
                  textContent = data.answer;
                }
            }

            // 如果有文本内容，添加到显示区域（主要用于text_chunk事件）
            if (textContent && textContent.trim()) {
              // 简化处理逻辑，直接累积文本内容
              // 不再尝试检测和处理代码块，避免过早补全
              streamContent.value += textContent;

              // 自动滚动到底部
              nextTick(() => {
                if (streamContentRef.value) {
                  streamContentRef.value.scrollTop = streamContentRef.value.scrollHeight;
                }
              });
            }
          } catch (error) {
            console.error('解析流式数据失败:', error);
          }
        }

      } catch (error) {
        console.error('启动新流式计算失败:', error);
        let errorMessage = '启动新流式计算失败';

        if (error.name === 'TypeError' && error.message.includes('getReader')) {
          errorMessage = '流式响应处理失败，请检查网络连接';
        } else if (error.message.includes('HTTP error')) {
          errorMessage = `服务器响应错误: ${error.message}`;
        } else {
          errorMessage = `计算失败: ${error.message}`;
        }

        ElMessage.error(errorMessage);
        isStreamCalculating.value = false;
      }
    };









    // 详情模式数据回显函数
    const loadDetailModeData = () => {
      console.log('详情模式：开始回显计算结果');

      // 如果有计算结果，直接使用
      if (props.formData.calculationResult) {
        calculationResult.value = props.formData.calculationResult;
        console.log('使用 formData.calculationResult:', calculationResult.value);
      }

      // 如果有报告数据，尝试从中提取计算过程
      if (props.formData.reportData) {
        try {
          let reportContent = null;

          // 尝试从不同字段获取报告内容
          if (props.formData.reportData.reportContent) {
            if (typeof props.formData.reportData.reportContent === 'string') {
              reportContent = JSON.parse(props.formData.reportData.reportContent);
            } else {
              reportContent = props.formData.reportData.reportContent;
            }
          } else if (props.formData.reportData.content) {
            if (typeof props.formData.reportData.content === 'string') {
              reportContent = JSON.parse(props.formData.reportData.content);
            } else {
              reportContent = props.formData.reportData.content;
            }
          }

          // 如果有报告内容，提取计算过程用于显示
          if (reportContent) {
            console.log('从报告内容中提取计算过程:', reportContent);

            // 尝试获取计算过程文本
            let calculationProcess = '';

            if (reportContent.calculationProcess) {
              calculationProcess = Array.isArray(reportContent.calculationProcess)
                ? reportContent.calculationProcess.join('\n\n')
                : reportContent.calculationProcess;
            } else if (reportContent.streamContent) {
              calculationProcess = reportContent.streamContent;
            } else if (reportContent.content) {
              calculationProcess = reportContent.content;
            }

            if (calculationProcess) {
              streamContent.value = calculationProcess;
              console.log('设置流式内容用于显示:', streamContent.value.substring(0, 200) + '...');
            }

            // 确保有计算结果
            if (!calculationResult.value && reportContent.calculationResult) {
              calculationResult.value = reportContent.calculationResult;
            } else if (!calculationResult.value && reportContent.calculatedPrice) {
              calculationResult.value = {
                success: true,
                totalCost: reportContent.calculatedPrice,
                calculationProcess: [calculationProcess]
              };
            }
          }
        } catch (error) {
          console.error('解析报告内容失败:', error);
        }
      }

      // 确保在详情模式下总是有内容显示在 XMarkdown 中
      if (!streamContent.value) {
        console.log('详情模式：设置默认显示内容');

        // 尝试从不同来源获取显示内容
        let displayContent = '';

        // 1. 尝试从计算结果中获取
        if (props.formData.calculationResult && props.formData.calculationResult.calculationProcess) {
          if (Array.isArray(props.formData.calculationResult.calculationProcess)) {
            displayContent = props.formData.calculationResult.calculationProcess.join('\n\n');
          } else {
            displayContent = props.formData.calculationResult.calculationProcess;
          }
        }

        // 2. 如果还没有内容，创建基于价格的显示
        if (!displayContent && props.formData.reportData && props.formData.reportData.calculatedPrice) {
          displayContent = `## 计算结果\n\n**项目最高限价：** ¥${formatPrice(props.formData.reportData.calculatedPrice)}\n\n计算已完成，详细过程请查看完整报告。`;
        }

        // 3. 最后的默认显示
        if (!displayContent) {
          displayContent = '## 计算结果\n\n计算已完成，详细过程请查看报告。\n\n*如果您看到此消息，说明计算数据正在加载中...*';
        }

        streamContent.value = displayContent;
        console.log('详情模式：设置显示内容完成');
      }

      // 确保有计算结果对象
      if (!calculationResult.value && props.formData.reportData && props.formData.reportData.calculatedPrice) {
        calculationResult.value = {
          success: true,
          totalCost: props.formData.reportData.calculatedPrice,
          calculationProcess: [streamContent.value]
        };
        console.log('详情模式：创建计算结果对象');
      }

      console.log('详情模式回显完成');
      console.log('最终计算结果:', calculationResult.value);
      console.log('最终流式内容长度:', streamContent.value?.length || 0);
    };

    // 组件挂载时处理逻辑
    onMounted(() => {
      console.log('EngineCalculationStep 组件挂载');
      console.log('详情模式:', props.isDetailMode);
      console.log('计算结果:', props.formData.calculationResult);
      console.log('报告数据:', props.formData.reportData);

      if (props.isDetailMode) {
        // 详情模式：直接回显已有的计算结果
        loadDetailModeData();
      } else {
        // 非详情模式：延迟一点时间开始计算，让用户看到界面
        console.log('非详情模式：准备开始计算');
        setTimeout(() => {
          startStreamCalculationNew();
        }, 1000);
      }
    });

    // 监听详情模式下的数据变化
    watch(() => props.formData.reportData, (newVal) => {
      if (props.isDetailMode && newVal) {
        console.log('检测到报告数据变化，重新回显');
        // 延迟执行，确保数据完全加载
        setTimeout(() => {
          loadDetailModeData();
        }, 100);
      }
    }, { deep: true });

    // 监听计算结果变化
    watch(() => props.formData.calculationResult, (newVal) => {
      if (props.isDetailMode && newVal && !calculationResult.value) {
        console.log('检测到计算结果变化，更新显示');
        calculationResult.value = newVal;
      }
    }, { deep: true });

    return {
      calculationResult,
      isStreamCalculating,
      streamContent,
      streamContentRef,
      getCalculationMethodName,
      formatPrice,
      formatTimestamp,
      processMarkdown,
      startStreamCalculationNew,
      parseStreamResult,
      clearStreamContent,
      loadDetailModeData
    };
  }
}
</script>

<style scoped>
.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 10px;
}

.step-description {
  margin-bottom: 25px;
}

.calculation-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.project-info {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-info h4 {
  margin-bottom: 10px;
}

.project-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
  white-space: nowrap;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.training-data {
  margin-top: 15px;
  padding-top: 12px;
  border-top: 1px solid #e4e7ed;
}

.training-data h5 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
}

.training-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
}





.calculating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
}

.result {
  width: 100%;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.result-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.price-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
}

.price-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.price-value {
  font-size: 24px;
  font-weight: bold;
  color: #67c23a;
}

.calculation-thinking {
  margin-top: 20px;
  width: 100%;
}

.calculation-thinking h5 {
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.thinking-console {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
  position: relative;
}

.console-line {
  margin-bottom: 8px;
  word-break: break-word;
}

.timestamp {
  margin-right: 8px;
}

.console-cursor {
  animation: blink 1s infinite;
  position: absolute;
  bottom: 15px;
  left: 15px;
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

.calculation-steps {
  margin-top: 20px;
}

.calculation-steps h5 {
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  font-weight: bold;
}

.step-item .step-content {
  flex: 1;
  padding: 0;
}

.hint {
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}



.error-message {
  margin-top: 20px;
}

.stream-waiting {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #d4d4d4;
}

.waiting-info {
  text-align: center;
}

.waiting-info .calc-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.waiting-info .calc-badge i {
  margin-right: 8px;
  font-size: 20px;
}

.waiting-info .calc-description {
  font-size: 14px;
  margin: 0 0 20px 0;
  color: #909399;
  line-height: 1.5;
}

.calculation-info {
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-radius: 12px;
  color: white;
  text-align: center;
}

.calc-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}

.calc-badge i {
  margin-right: 8px;
  font-size: 20px;
}

.calc-description {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.el-button .el-icon-cpu,
.el-button .el-icon-video-play {
  margin-right: 6px;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 15px;
}

.button-group .el-button {
  flex: 1;
  max-width: 200px;
}

/* 流式计算样式 */
.stream-calculation {
  margin-top: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 400px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 0;
  flex-shrink: 0;
}

.stream-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.stream-status {
  display: flex;
  align-items: center;
}





.stream-content {
  padding: 15px;
  flex: 1;
  overflow-y: auto;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  border-radius: 0 0 4px 4px;
}

.stream-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.stream-cursor {
  display: inline-block;
  background-color: #d4d4d4;
  width: 2px;
  height: 20px;
  animation: blink 1s infinite;
  margin-left: 2px;
}

/* 基本样式 - 简化版 */
.stream-content {
  padding: 15px;
  background-color: #ffffff;
  border-radius: 4px;
  overflow-y: auto;
}

/* 流式文本基本样式 */
.stream-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

/* 调整Markdown内容的间距 */
.stream-text :deep(p) {
  margin: 0.5em 0;
}

.stream-text :deep(h1),
.stream-text :deep(h2),
.stream-text :deep(h3),
.stream-text :deep(h4),
.stream-text :deep(h5),
.stream-text :deep(h6) {
  margin: 1em 0 0.5em 0;
}

.stream-text :deep(ul),
.stream-text :deep(ol) {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.stream-text :deep(li) {
  margin: 0.2em 0;
}

/* 光标动画 */
.stream-cursor {
  display: inline-block;
  width: 2px;
  height: 20px;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}


</style>
<style>
.markdown-elxLanguage-header-div {
  top: auto !important;
}
</style>
