<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.PricingRulesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pricingRulesResultMap" type="org.springblade.modules.xjzs.pojo.entity.PricingRulesEntity">
        <id column="id" property="id"/>
        <result column="rule_name" property="ruleName"/>
        <result column="rule_type" property="ruleType"/>
        <result column="calculation_formula" property="calculationFormula"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, rule_name, rule_type, calculation_formula, remark, 
        create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectPricingRulesPage" resultMap="pricingRulesResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM xjzs_pricing_rules
        WHERE is_deleted = 0
        <if test="pricingRules.ruleName != null and pricingRules.ruleName != ''">
            AND rule_name LIKE CONCAT('%', #{pricingRules.ruleName}, '%')
        </if>
        <if test="pricingRules.ruleType != null and pricingRules.ruleType != ''">
            AND rule_type = #{pricingRules.ruleType}
        </if>
        <if test="pricingRules.status != null">
            AND status = #{pricingRules.status}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 导出数据查询 -->
    <select id="exportPricingRules" resultType="org.springblade.modules.xjzs.excel.PricingRulesExcel">
        SELECT 
            id,
            rule_name AS ruleName,
            rule_type AS ruleType,
            calculation_formula AS calculationFormula,
            remark,
            is_deleted AS isDeleted
        FROM xjzs_pricing_rules
        ${ew.customSqlSegment}
    </select>

</mapper>
