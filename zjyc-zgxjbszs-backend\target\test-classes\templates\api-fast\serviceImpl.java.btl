/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package ${package.ServiceImpl!};

import ${packageName!}.pojo.entity.${entityKey!}Entity;
import ${packageName!}.pojo.vo.${entityKey!}VO;
import ${packageName!}.excel.${entityKey!}Excel;
import ${packageName!}.mapper.${table.mapperName!};
import ${packageName!}.service.${table.serviceName!};
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
#if(hasSuperEntity){
import ${superServiceImplClassPackage!};
#}else{
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
#}
import java.util.List;

/**
 * ${table.comment!} 服务实现类
 *
 * <AUTHOR>
 * @since ${date!}
 */
@Service
#if(hasSuperEntity){
public class ${table.serviceImplName!} extends ${superServiceImplClass!}<${table.mapperName!}, ${entity!}> implements ${table.serviceName!} {
#}else{
public class ${table.serviceImplName!} extends ServiceImpl<${table.mapperName!}, ${entity!}> implements ${table.serviceName!} {
#}

	@Override
	public IPage<${entityKey!}VO> select${entityKey!}Page(IPage<${entityKey!}VO> page, ${entityKey!}VO ${entityKeyPath!}) {
		return page.setRecords(baseMapper.select${entityKey!}Page(page, ${entityKeyPath!}));
	}

	@Override
	public List<${entityKey!}Excel> export${entityKey!}(Wrapper<${entityKey!}Entity> queryWrapper) {
		List<${entityKey!}Excel> ${entityKeyPath!}List = baseMapper.export${entityKey!}(queryWrapper);
		//${entityKeyPath!}List.forEach(${entityKeyPath!} -> {
		//	${entityKeyPath!}.setTypeName(DictCache.getValue(DictEnum.YES_NO, ${entity!}.getType()));
		//});
		return ${entityKeyPath!}List;
	}

}
