<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.system.mapper.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deptResultMap" type="org.springblade.modules.system.pojo.entity.Dept">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="dept_name" property="deptName"/>
        <result column="full_name" property="fullName"/>
        <result column="ancestors" property="ancestors"/>
        <result column="dept_category" property="deptCategory"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="deptVOResultMap" type="org.springblade.modules.system.pojo.vo.DeptVO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="dept_name" property="deptName"/>
        <result column="full_name" property="fullName"/>
        <result column="ancestors" property="ancestors"/>
        <result column="dept_category" property="deptCategory"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <select id="lazyList" resultMap="deptVOResultMap">
        SELECT
            dept.* ,
            (
                SELECT
                    CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
                FROM
                    blade_dept
                WHERE
                    parent_id = dept.id and is_deleted = 0
            ) AS "has_children"
        FROM
            blade_dept dept
        WHERE dept.is_deleted = 0
        <if test="param1!=null and param1!=''">
            and dept.tenant_id = #{param1}
        </if>
        <if test="param2!=null">
            and dept.parent_id = #{param2}
        </if>
        <if test="param3.deptName!=null and param3.deptName!=''">
            and dept.dept_name like concat(concat('%', #{param3.deptName}),'%')
        </if>
        <if test="param3.fullName!=null and param3.fullName!=''">
            and dept.full_name like concat(concat('%', #{param3.fullName}),'%')
        </if>
        ORDER BY dept.sort
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, dept_name as title, id as "value", id as "key" from blade_dept where is_deleted = 0
        <if test="_parameter!=null and _parameter!=''">
            and tenant_id = #{_parameter}
        </if>
        ORDER BY sort
    </select>

    <select id="lazyTree" resultMap="treeNodeResultMap" >
        SELECT
            dept.id,
            dept.parent_id,
            dept.dept_name AS title,
            dept.id AS "value",
            dept.id AS "key",
            (
                SELECT
                    CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
                FROM
                    blade_dept
                WHERE
                    parent_id = dept.id and is_deleted = 0
            ) AS "has_children"
        FROM
            blade_dept dept
        WHERE
            dept.parent_id = #{param2} AND dept.is_deleted = 0
        <if test="param1!=null and param1!=''">
            and dept.tenant_id = #{param1}
        </if>
        ORDER BY dept.sort
    </select>

    <select id="getDeptNames" resultType="java.lang.String">
        SELECT
        dept_name
        FROM
        blade_dept
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

</mapper>
